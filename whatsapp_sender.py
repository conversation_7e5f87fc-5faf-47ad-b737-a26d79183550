#!/usr/bin/env python3
"""
WhatsApp Sender for WhatsApp Client
Handles sending messages via WhatsApp API
"""

import time
import logging
import requests
import json
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from config import WHATSAPP_API_BASE_URL, SENDING_CONFIG, ERROR_CONFIG

class WhatsAppSender:
    """<PERSON>les sending WhatsApp messages via the API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sent_messages = []
        self.failed_messages = []
        self.consecutive_failures = 0
        
    def send_message(self, recipient: str, message: str) -> Tuple[bool, str]:
        """
        Send a WhatsApp message to a recipient
        
        Args:
            recipient: Phone number or JID of the recipient
            message: Message text to send
            
        Returns:
            Tuple of (success: bool, status_message: str)
        """
        try:
            # Validate input
            if not recipient:
                return False, "Recipient must be provided"
            
            if not message.strip():
                return False, "Message cannot be empty"
            
            url = f"{WHATSAPP_API_BASE_URL}/send"
            payload = {
                "recipient": recipient,
                "message": message,
            }
            
            self.logger.info(f"Sending message to {recipient}")
            
            response = requests.post(url, json=payload, timeout=30)
            
            # Check if the request was successful
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                status_message = result.get("message", "Unknown response")
                
                if success:
                    self.consecutive_failures = 0
                    self._log_successful_send(recipient, message, status_message)
                else:
                    self.consecutive_failures += 1
                    self._log_failed_send(recipient, message, status_message)
                
                return success, status_message
            else:
                error_msg = f"HTTP {response.status_code} - {response.text}"
                self.consecutive_failures += 1
                self._log_failed_send(recipient, message, error_msg)
                return False, error_msg
                
        except requests.RequestException as e:
            error_msg = f"Request error: {str(e)}"
            self.consecutive_failures += 1
            self._log_failed_send(recipient, message, error_msg)
            return False, error_msg
        except json.JSONDecodeError:
            error_msg = f"Error parsing response: {response.text}"
            self.consecutive_failures += 1
            self._log_failed_send(recipient, message, error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            self.consecutive_failures += 1
            self._log_failed_send(recipient, message, error_msg)
            return False, error_msg
    
    def send_message_with_retry(self, recipient: str, message: str) -> Tuple[bool, str]:
        """
        Send message with retry logic
        
        Args:
            recipient: Phone number or JID of the recipient
            message: Message text to send
            
        Returns:
            Tuple of (success: bool, final_status_message: str)
        """
        max_retries = SENDING_CONFIG['max_retries']
        retry_delay = SENDING_CONFIG['retry_delay']
        
        for attempt in range(max_retries + 1):  # +1 for initial attempt
            success, status_message = self.send_message(recipient, message)
            
            if success:
                if attempt > 0:
                    self.logger.info(f"Message sent successfully on attempt {attempt + 1}")
                return True, status_message
            
            if attempt < max_retries:
                self.logger.warning(f"Attempt {attempt + 1} failed: {status_message}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                self.logger.error(f"All {max_retries + 1} attempts failed for {recipient}")
        
        return False, status_message
    
    def send_batch_messages(self, messages: List[Dict[str, str]]) -> Dict[str, any]:
        """
        Send a batch of messages with proper delays and error handling
        
        Args:
            messages: List of message dictionaries with 'recipient', 'message', and optional metadata
            
        Returns:
            Dictionary with batch sending results
        """
        total_messages = len(messages)
        successful_sends = 0
        failed_sends = 0
        batch_start_time = datetime.now()
        
        self.logger.info(f"Starting batch send of {total_messages} messages")
        
        for i, msg_data in enumerate(messages):
            # Check for too many consecutive failures
            if self.consecutive_failures >= ERROR_CONFIG['max_consecutive_failures']:
                self.logger.error(f"Too many consecutive failures ({self.consecutive_failures}), stopping batch")
                break
            
            recipient = msg_data.get('recipient')
            message = msg_data.get('message')
            
            if not recipient or not message:
                self.logger.warning(f"Skipping message {i+1}: missing recipient or message")
                failed_sends += 1
                continue
            
            # Send message with retry
            success, status = self.send_message_with_retry(recipient, message)
            
            if success:
                successful_sends += 1
                self.logger.info(f"✅ Message {i+1}/{total_messages} sent successfully to {recipient}")
            else:
                failed_sends += 1
                self.logger.error(f"❌ Message {i+1}/{total_messages} failed to {recipient}: {status}")
                
                if not ERROR_CONFIG['continue_on_error']:
                    self.logger.error("Stopping batch due to error (continue_on_error=False)")
                    break
            
            # Add delay between messages (except for the last one)
            if i < total_messages - 1:
                delay = SENDING_CONFIG['delay_between_messages']
                self.logger.debug(f"Waiting {delay}s before next message...")
                time.sleep(delay)
            
            # Add batch delay if we've reached batch size
            if ((i + 1) % SENDING_CONFIG['batch_size'] == 0 and 
                i < total_messages - 1):
                batch_delay = SENDING_CONFIG['delay_between_batches']
                self.logger.info(f"Batch of {SENDING_CONFIG['batch_size']} completed. Waiting {batch_delay}s...")
                time.sleep(batch_delay)
        
        batch_end_time = datetime.now()
        duration = (batch_end_time - batch_start_time).total_seconds()
        
        results = {
            'total_messages': total_messages,
            'successful_sends': successful_sends,
            'failed_sends': failed_sends,
            'success_rate': (successful_sends / total_messages * 100) if total_messages > 0 else 0,
            'duration_seconds': duration,
            'start_time': batch_start_time,
            'end_time': batch_end_time
        }
        
        self.logger.info(f"Batch completed: {successful_sends}/{total_messages} successful ({results['success_rate']:.1f}%)")
        return results
    
    def _log_successful_send(self, recipient: str, message: str, status: str):
        """Log successful message send"""
        self.sent_messages.append({
            'recipient': recipient,
            'message': message[:100] + '...' if len(message) > 100 else message,
            'status': status,
            'timestamp': datetime.now(),
            'success': True
        })
    
    def _log_failed_send(self, recipient: str, message: str, error: str):
        """Log failed message send"""
        self.failed_messages.append({
            'recipient': recipient,
            'message': message[:100] + '...' if len(message) > 100 else message,
            'error': error,
            'timestamp': datetime.now(),
            'success': False
        })
    
    def get_sending_stats(self) -> Dict[str, any]:
        """Get statistics about sending performance"""
        total_attempts = len(self.sent_messages) + len(self.failed_messages)
        
        return {
            'total_attempts': total_attempts,
            'successful_sends': len(self.sent_messages),
            'failed_sends': len(self.failed_messages),
            'success_rate': (len(self.sent_messages) / total_attempts * 100) if total_attempts > 0 else 0,
            'consecutive_failures': self.consecutive_failures
        }
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        Test connection to WhatsApp API

        Returns:
            Tuple of (success: bool, status_message: str)
        """
        try:
            # Test by making a simple request to the send endpoint with invalid data
            # This will return an error but confirms the API is running
            url = f"{WHATSAPP_API_BASE_URL}/send"
            payload = {"recipient": "", "message": ""}  # Invalid payload to test endpoint

            response = requests.post(url, json=payload, timeout=10)

            # We expect a 400 error for invalid data, which means the API is working
            if response.status_code == 400:
                return True, "WhatsApp API connection successful"
            elif response.status_code == 200:
                return True, "WhatsApp API connection successful"
            elif response.status_code == 500:
                # Server error might indicate WhatsApp client not connected
                return False, "WhatsApp API is running but WhatsApp client may not be connected"
            else:
                return False, f"WhatsApp API returned unexpected status {response.status_code}"

        except requests.ConnectionError:
            return False, "Cannot connect to WhatsApp API - is the whatsapp-bridge running on port 8080?"
        except requests.RequestException as e:
            return False, f"Cannot connect to WhatsApp API: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error testing connection: {str(e)}"

def main():
    """Test the WhatsApp sender"""
    sender = WhatsAppSender()
    
    # Test connection
    connected, status = sender.test_connection()
    print(f"Connection test: {status}")
    
    if connected:
        # Test sending a message (replace with a real phone number for testing)
        test_recipient = "6591234567"  # Replace with your test number
        test_message = "This is a test message from the WhatsApp client."
        
        print(f"Sending test message to {test_recipient}...")
        success, result = sender.send_message_with_retry(test_recipient, test_message)
        
        if success:
            print(f"✅ Test message sent successfully: {result}")
        else:
            print(f"❌ Test message failed: {result}")
        
        # Show stats
        stats = sender.get_sending_stats()
        print(f"Sending stats: {stats}")
    else:
        print("Cannot test sending - API connection failed")

if __name__ == "__main__":
    main()
