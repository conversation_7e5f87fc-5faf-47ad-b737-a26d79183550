# WhatsApp Client for PropertyGuru

A Python application that reads PropertyGuru property URLs from CSV files and automatically sends personalized WhatsApp messages to contacts about each property.

## Features

- 📊 **CSV Processing**: Read property data and contact information from CSV files
- 📱 **WhatsApp Integration**: Send messages via WhatsApp API (requires whatsapp-mcp-server)
- 📝 **Message Templates**: Multiple customizable message templates
- 🔄 **Batch Processing**: Send messages in batches with configurable delays
- 📈 **Progress Tracking**: Detailed logging and result reporting
- 🛡️ **Error Handling**: Retry logic and graceful error handling
- 📋 **Validation**: Phone number and URL validation

## Prerequisites

1. **WhatsApp MCP Server**: The whatsapp-mcp-server must be running on `localhost:8080`
2. **Python 3.7+**: Required for the application
3. **CSV File**: Properly formatted CSV with property and contact data

## Installation

1. Navigate to the whatsapp-client directory:
```bash
cd whatsapp-client
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create required directories (done automatically on first run):
```bash
mkdir -p logs output sample_data templates
```

## CSV File Format

Your CSV file must contain these required columns:
- `contact_phone`: Phone number (with country code, no + symbol)
- `property_url`: PropertyGuru URL

Optional columns (will enhance the messages):
- `contact_name`: Contact's name
- `property_title`: Property title
- `property_price`: Property price
- `property_address`: Property address
- `bedrooms`: Number of bedrooms
- `area`: Property area

### Example CSV:
```csv
contact_name,contact_phone,property_url,property_title,property_price,property_address,bedrooms,area
John Doe,6591234567,https://www.propertyguru.com.sg/listing/for-sale-bartley-residences-25585441,Bartley Residences,S$ 2100000,16 min from Serangoon MRT,3,1066 sqft
Alice Smith,6598765432,https://www.propertyguru.com.sg/listing/for-sale-grandeur-8-25581307,Grandeur 8,S$ 2180000,9 min from Ang Mo Kio MRT,3,1411 sqft
```

## Usage

### Basic Usage
```bash
python main.py your_properties.csv
```

### With Custom Template
```bash
python main.py your_properties.csv --template professional_inquiry
```

### Test Connection
```bash
python main.py --test-connection
```

### List Available Templates
```bash
python main.py --list-templates
```

### Dry Run (Process but don't send)
```bash
python main.py your_properties.csv --dry-run
```

## Message Templates

### Available Templates:

1. **property_inquiry** (default): Detailed property information with friendly tone
2. **simple_inquiry**: Short and simple message
3. **professional_inquiry**: Formal business tone

### Template Preview:
```bash
python main.py --list-templates
```

## Configuration

Edit `config.py` to customize:

- **Message templates**: Modify `MESSAGE_TEMPLATES`
- **Sending delays**: Adjust `SENDING_CONFIG`
- **Phone formatting**: Configure `PHONE_CONFIG`
- **API settings**: Update `WHATSAPP_API_BASE_URL`

## Output Files

After processing, check the `output/` directory for:

- `sending_results_YYYYMMDD_HHMMSS.csv`: Detailed results for each message
- `failed_messages_YYYYMMDD_HHMMSS.csv`: Failed messages for retry
- `success_report_YYYYMMDD_HHMMSS.txt`: Summary report

## Logging

Logs are saved to `logs/whatsapp_client.log` and displayed in the console.

## Error Handling

The application includes:
- **Retry Logic**: Failed messages are retried up to 3 times
- **Batch Processing**: Messages sent in configurable batches with delays
- **Validation**: Phone numbers and URLs are validated before sending
- **Graceful Failures**: Processing continues even if some messages fail

## Safety Features

- **Rate Limiting**: Configurable delays between messages and batches
- **Validation**: Input validation for phone numbers and URLs
- **Dry Run Mode**: Test processing without sending messages
- **Connection Testing**: Verify API connection before processing

## Troubleshooting

### Common Issues:

1. **"Cannot connect to WhatsApp API"**
   - Ensure whatsapp-mcp-server is running on localhost:8080
   - Check if the WhatsApp bridge is properly connected

2. **"Missing required columns"**
   - Verify your CSV has `contact_phone` and `property_url` columns
   - Check column names match exactly (case-sensitive)

3. **"Invalid phone number"**
   - Use format: 6591234567 (country code + number, no + or spaces)
   - Singapore numbers should start with 65

4. **"Invalid PropertyGuru URL"**
   - URLs must be from propertyguru.com.sg domain
   - Include full URL with https://

### Debug Mode:
Edit `config.py` and set logging level to 'DEBUG' for detailed logs.

## Integration with PropertyGuru Scraper

This client works perfectly with the PropertyGuru scraper output:

1. Run the PropertyGuru scraper to generate CSV files
2. Add contact information to the CSV
3. Use this WhatsApp client to send property information to contacts

## Example Workflow

1. **Prepare CSV**: Export PropertyGuru data and add contact information
2. **Test Connection**: `python main.py --test-connection`
3. **Preview Templates**: `python main.py --list-templates`
4. **Dry Run**: `python main.py data.csv --dry-run`
5. **Send Messages**: `python main.py data.csv --template property_inquiry`
6. **Check Results**: Review files in `output/` directory

## Support

For issues or questions:
1. Check the logs in `logs/whatsapp_client.log`
2. Verify WhatsApp MCP server is running
3. Test with the sample CSV file first
4. Use dry-run mode to test processing without sending

## License

This project is part of the real estate agency automation suite.
