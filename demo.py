#!/usr/bin/env python3
"""
WhatsApp Client Demo
Demonstrates the complete workflow of the WhatsApp client
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from csv_processor import CSVProcessor
from message_composer import MessageComposer
from whatsapp_sender import WhatsAppSender

def demo_csv_processing():
    """Demo CSV processing functionality"""
    print("🔄 Demo: CSV Processing")
    print("-" * 30)
    
    processor = CSVProcessor()
    sample_file = "sample_data/sample_properties.csv"
    
    if not os.path.exists(sample_file):
        print(f"❌ Sample file not found: {sample_file}")
        return False
    
    try:
        # Read CSV
        data = processor.read_csv(sample_file)
        print(f"✅ Successfully read {len(data)} rows from CSV")
        
        # Show first row
        if data:
            print(f"📋 Sample row: {data[0]}")
            
            # Extract property details
            details = processor.extract_property_details_from_csv(data[0])
            print(f"🏠 Property details: {details}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_message_composition():
    """Demo message composition functionality"""
    print("\n📝 Demo: Message Composition")
    print("-" * 35)
    
    composer = MessageComposer()
    
    # Sample data
    contact_data = {
        'contact_name': 'Demo User',
        'property_url': 'https://www.propertyguru.com.sg/listing/for-sale-demo-property-12345',
        'contact_phone': '6591234567'
    }
    
    property_details = {
        'title': 'Beautiful Demo Property',
        'price': 'S$ 1,800,000',
        'address': 'Prime Location, Singapore',
        'bedrooms': '3',
        'area': '1,200 sqft'
    }
    
    try:
        # Compose message
        message = composer.compose_message(contact_data, property_details)
        print(f"✅ Message composed ({len(message)} characters)")
        
        # Show preview
        print("\n📄 Message Preview:")
        print("=" * 50)
        print(message[:200] + "..." if len(message) > 200 else message)
        print("=" * 50)
        
        # Validate
        issues = composer.validate_message(message)
        if issues:
            print(f"⚠️  Validation issues: {issues}")
        else:
            print("✅ Message validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_whatsapp_connection():
    """Demo WhatsApp connection testing"""
    print("\n📱 Demo: WhatsApp Connection")
    print("-" * 32)
    
    sender = WhatsAppSender()
    
    try:
        # Test connection
        connected, status = sender.test_connection()
        if connected:
            print(f"✅ Connection test: {status}")
        else:
            print(f"❌ Connection test: {status}")
        
        # Show stats
        stats = sender.get_sending_stats()
        print(f"📊 Sending stats: {stats}")
        
        return connected
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_full_workflow():
    """Demo the complete workflow"""
    print("\n🎯 Demo: Complete Workflow")
    print("-" * 30)
    
    try:
        # Initialize components
        processor = CSVProcessor()
        composer = MessageComposer()
        sender = WhatsAppSender()
        
        # Read sample CSV
        sample_file = "sample_data/sample_properties.csv"
        data = processor.read_csv(sample_file)
        print(f"✅ Loaded {len(data)} records")
        
        # Process first record as example
        if data:
            row = data[0]
            print(f"📋 Processing: {row['contact_name']} - {row['property_title']}")
            
            # Extract property details
            property_details = processor.extract_property_details_from_csv(row)
            
            # Compose message
            message = composer.compose_message(row, property_details)
            print(f"📝 Message composed ({len(message)} characters)")
            
            # Test connection (don't actually send in demo)
            connected, status = sender.test_connection()
            if connected:
                print(f"📱 WhatsApp API ready: {status}")
                print("✅ Ready to send messages!")
            else:
                print(f"❌ WhatsApp API not ready: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_usage_examples():
    """Show practical usage examples"""
    print("\n💡 Usage Examples")
    print("-" * 18)
    
    examples = [
        ("Test connection", "python main.py --test-connection"),
        ("List templates", "python main.py --list-templates"),
        ("Dry run", "python main.py sample_data/sample_properties.csv --dry-run"),
        ("Send messages", "python main.py your_properties.csv"),
        ("Use template", "python main.py your_file.csv --template professional_inquiry"),
    ]
    
    for desc, cmd in examples:
        print(f"  {desc:15}: {cmd}")

def main():
    """Run the complete demo"""
    print("🚀 WhatsApp Client Demo")
    print("=" * 40)
    print("This demo shows all the features of the WhatsApp client.")
    print()
    
    # Run demos
    demos = [
        ("CSV Processing", demo_csv_processing),
        ("Message Composition", demo_message_composition),
        ("WhatsApp Connection", demo_whatsapp_connection),
        ("Complete Workflow", demo_full_workflow),
    ]
    
    passed = 0
    total = len(demos)
    
    for name, demo_func in demos:
        try:
            if demo_func():
                passed += 1
            else:
                print(f"❌ {name} demo failed")
        except Exception as e:
            print(f"❌ {name} demo error: {e}")
        
        time.sleep(1)  # Brief pause between demos
    
    # Show results
    print(f"\n{'='*40}")
    print(f"Demo Results: {passed}/{total} demos passed")
    
    if passed == total:
        print("🎉 All demos passed! The WhatsApp client is working perfectly.")
    else:
        print("⚠️  Some demos failed. Check the errors above.")
    
    # Show usage examples
    show_usage_examples()
    
    print(f"\n📁 Files created during demo:")
    if os.path.exists("output"):
        for file in os.listdir("output"):
            print(f"  - output/{file}")
    
    print(f"\n🎯 Next Steps:")
    print("1. Prepare your CSV file with property data and contacts")
    print("2. Test with: python main.py your_file.csv --dry-run")
    print("3. Send messages: python main.py your_file.csv")
    print("4. Check results in the output/ folder")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
