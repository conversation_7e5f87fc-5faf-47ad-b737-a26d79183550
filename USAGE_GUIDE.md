# WhatsApp Client Usage Guide

## 🎯 Complete Setup and Usage Instructions

### Prerequisites
1. **WhatsApp MCP Server** must be running on `localhost:8080`
2. **Python 3.7+** installed
3. **CSV file** with PropertyGuru URLs and contact information

### Quick Setup
```bash
cd whatsapp-client
python setup.py          # Install dependencies and run tests
python quick_start.py     # Interactive guide
```

## 📊 CSV File Format

### Required Columns:
- `contact_phone`: Phone number (format: 6591234567)
- `property_url`: PropertyGuru URL

### Optional Columns (enhance messages):
- `contact_name`: Contact's name
- `property_title`: Property title  
- `property_price`: Property price
- `property_address`: Property address
- `bedrooms`: Number of bedrooms
- `area`: Property area

### Example CSV:
```csv
contact_name,contact_phone,property_url,property_title,property_price
John <PERSON>,6591234567,https://www.propertyguru.com.sg/listing/for-sale-bartley-residences-25585441,<PERSON><PERSON> Residences,S$ 2100000
<PERSON>,6598765432,https://www.propertyguru.com.sg/listing/for-sale-grandeur-8-25581307,Grandeur 8,S$ 2180000
```

## 🚀 Usage Commands

### 1. Test Connection
```bash
python main.py --test-connection
```

### 2. List Message Templates
```bash
python main.py --list-templates
```

### 3. Process CSV File
```bash
# Basic usage
python main.py your_properties.csv

# With specific template
python main.py your_properties.csv --template professional_inquiry

# Dry run (test without sending)
python main.py your_properties.csv --dry-run
```

## 📝 Message Templates

### 1. property_inquiry (default)
- Detailed property information
- Friendly, personal tone
- Includes all available property details

### 2. simple_inquiry
- Short and concise
- Basic property information
- Quick to read

### 3. professional_inquiry
- Formal business tone
- Professional language
- Suitable for business contacts

## 📋 Example Message Output

```
Hi Alice Smith,

I hope this message finds you well! I came across this property listing and thought it might interest you:

🏠 *Property Details:*
📍 *Bartley Residences*
💰 Price: S$ 2,100,000
📍 Location: 16 min (1.37 km) from NE12 Serangoon MRT Station
📐 Area: 1,066 sqft

🔗 *View Full Listing:* https://www.propertyguru.com.sg/listing/for-sale-bartley-residences-25585441

Would you like to know more about this property or schedule a viewing? I'm happy to help with any questions you might have.

Best regards!
```

## 📁 Output Files

After processing, check the `output/` directory for:

- **`sending_results_YYYYMMDD_HHMMSS.csv`**: Detailed results for each message
- **`failed_messages_YYYYMMDD_HHMMSS.csv`**: Failed messages for retry
- **`success_report_YYYYMMDD_HHMMSS.txt`**: Summary report

## ⚙️ Configuration

Edit `config.py` to customize:

### Message Settings:
```python
MESSAGE_CONFIG = {
    'default_template': 'property_inquiry',
    'max_message_length': 4000,
    'include_property_details': True,
    'personalize_messages': True
}
```

### Sending Settings:
```python
SENDING_CONFIG = {
    'batch_size': 10,
    'delay_between_messages': 3,
    'delay_between_batches': 30,
    'max_retries': 3,
}
```

## 🔧 Troubleshooting

### Common Issues:

1. **"Cannot connect to WhatsApp API"**
   - Start whatsapp-mcp-server: `cd ../whatsapp-mcp-server && python main.py`
   - Check if server is running on localhost:8080

2. **"Missing required columns"**
   - Ensure CSV has `contact_phone` and `property_url` columns
   - Check column names match exactly (case-sensitive)

3. **"Invalid phone number"**
   - Use format: 6591234567 (country code + number, no + or spaces)
   - Singapore numbers should start with 65

4. **"Invalid PropertyGuru URL"**
   - URLs must be from propertyguru.com.sg domain
   - Include full URL with https://

### Debug Steps:
1. Run tests: `python test_client.py`
2. Test connection: `python main.py --test-connection`
3. Try dry run: `python main.py your_file.csv --dry-run`
4. Check logs: `logs/whatsapp_client.log`

## 🔄 Integration with PropertyGuru Scraper

Perfect workflow:
1. **Run PropertyGuru scraper** to get property data
2. **Add contact information** to the generated CSV
3. **Use WhatsApp client** to send property info to contacts

### Example:
```bash
# 1. Run PropertyGuru scraper
cd ../propertyguru_scraper_project
python main.py

# 2. Add contacts to the generated CSV file
# Edit: output/propertyguru_complete_YYYYMMDD_HHMMSS.csv

# 3. Send via WhatsApp
cd ../whatsapp-client
python main.py ../propertyguru_scraper_project/output/your_file.csv
```

## 📊 Batch Processing Features

- **Rate Limiting**: Configurable delays between messages
- **Batch Processing**: Send messages in groups with longer delays
- **Retry Logic**: Automatic retry for failed messages
- **Progress Tracking**: Real-time progress and statistics
- **Error Handling**: Continue processing even if some messages fail

## 🛡️ Safety Features

- **Validation**: Phone numbers and URLs validated before sending
- **Dry Run Mode**: Test processing without sending messages
- **Connection Testing**: Verify API connection before processing
- **Message Length Limits**: Automatic truncation for long messages
- **Graceful Error Handling**: Detailed error reporting and recovery

## 📈 Success Tracking

The application tracks:
- Total messages processed
- Successful sends vs failures
- Processing time and performance
- Detailed logs for debugging
- Failed messages for retry

## 🎯 Best Practices

1. **Test First**: Always use `--test-connection` and `--dry-run`
2. **Start Small**: Test with a few contacts before large batches
3. **Use Delays**: Don't overwhelm the WhatsApp API
4. **Monitor Logs**: Check logs for any issues
5. **Backup Data**: Keep copies of your CSV files
6. **Personalize**: Use contact names for better engagement

## 📞 Support

For issues:
1. Check `logs/whatsapp_client.log`
2. Run `python test_client.py`
3. Verify WhatsApp MCP server is running
4. Test with sample data first
