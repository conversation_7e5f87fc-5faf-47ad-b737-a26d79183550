#!/usr/bin/env python3
"""
Quick Start Guide for WhatsApp Client
Interactive setup and demo
"""

import os
import sys
import csv
from datetime import datetime

def show_welcome():
    """Show welcome message"""
    print("🚀 WhatsApp Client Quick Start")
    print("=" * 40)
    print("This tool helps you send PropertyGuru property information via WhatsApp.")
    print()

def check_prerequisites():
    """Check if prerequisites are met"""
    print("Checking prerequisites...")
    
    # Check if whatsapp-mcp-server exists
    mcp_server_path = "../whatsapp-mcp-server"
    if os.path.exists(mcp_server_path):
        print("✅ WhatsApp MCP Server found")
    else:
        print("❌ WhatsApp MCP Server not found")
        print(f"   Expected location: {mcp_server_path}")
        return False
    
    # Check if sample data exists
    sample_file = "sample_data/sample_properties.csv"
    if os.path.exists(sample_file):
        print("✅ Sample data file found")
    else:
        print("❌ Sample data file not found")
        return False
    
    return True

def show_csv_format():
    """Show CSV format requirements"""
    print("\n📋 CSV File Format Requirements:")
    print("-" * 35)
    print("Required columns:")
    print("  • contact_phone: Phone number (e.g., 6591234567)")
    print("  • property_url: PropertyGuru URL")
    print()
    print("Optional columns (enhance messages):")
    print("  • contact_name: Contact's name")
    print("  • property_title: Property title")
    print("  • property_price: Property price")
    print("  • property_address: Property address")
    print("  • bedrooms: Number of bedrooms")
    print("  • area: Property area")
    print()

def show_sample_csv():
    """Show sample CSV content"""
    sample_file = "sample_data/sample_properties.csv"
    if os.path.exists(sample_file):
        print("📄 Sample CSV Content:")
        print("-" * 25)
        with open(sample_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:3]  # Show first 3 lines
            for line in lines:
                print(f"  {line.strip()}")
        print("  ...")
        print()

def create_custom_csv():
    """Help user create a custom CSV file"""
    print("📝 Create Custom CSV File")
    print("-" * 25)
    
    filename = input("Enter filename for your CSV (e.g., my_properties.csv): ").strip()
    if not filename:
        filename = f"my_properties_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    if not filename.endswith('.csv'):
        filename += '.csv'
    
    print(f"Creating: {filename}")
    print("Enter property data (press Enter with empty name to finish):")
    
    properties = []
    while True:
        print(f"\nProperty {len(properties) + 1}:")
        name = input("  Contact name: ").strip()
        if not name:
            break
        
        phone = input("  Contact phone (e.g., 6591234567): ").strip()
        url = input("  Property URL: ").strip()
        title = input("  Property title (optional): ").strip()
        price = input("  Property price (optional): ").strip()
        
        properties.append({
            'contact_name': name,
            'contact_phone': phone,
            'property_url': url,
            'property_title': title,
            'property_price': price,
            'property_address': '',
            'bedrooms': '',
            'area': ''
        })
    
    if properties:
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['contact_name', 'contact_phone', 'property_url', 
                             'property_title', 'property_price', 'property_address', 
                             'bedrooms', 'area']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(properties)
            
            print(f"✅ CSV file created: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Error creating CSV: {e}")
            return None
    else:
        print("No properties entered.")
        return None

def show_usage_examples():
    """Show usage examples"""
    print("💡 Usage Examples:")
    print("-" * 18)
    print("1. Test connection:")
    print("   python main.py --test-connection")
    print()
    print("2. List message templates:")
    print("   python main.py --list-templates")
    print()
    print("3. Process sample CSV:")
    print("   python main.py sample_data/sample_properties.csv")
    print()
    print("4. Use specific template:")
    print("   python main.py my_file.csv --template professional_inquiry")
    print()
    print("5. Dry run (test without sending):")
    print("   python main.py my_file.csv --dry-run")
    print()

def interactive_demo():
    """Run interactive demo"""
    print("🎯 Interactive Demo")
    print("-" * 17)
    
    while True:
        print("\nWhat would you like to do?")
        print("1. Test WhatsApp connection")
        print("2. View message templates")
        print("3. Process sample CSV (dry run)")
        print("4. Create custom CSV file")
        print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            print("\nTesting WhatsApp connection...")
            os.system("python main.py --test-connection")
        
        elif choice == '2':
            print("\nViewing message templates...")
            os.system("python main.py --list-templates")
        
        elif choice == '3':
            print("\nProcessing sample CSV (dry run)...")
            os.system("python main.py sample_data/sample_properties.csv --dry-run")
        
        elif choice == '4':
            csv_file = create_custom_csv()
            if csv_file:
                test_it = input(f"\nTest the new CSV file '{csv_file}'? (y/n): ").strip().lower()
                if test_it == 'y':
                    os.system(f"python main.py {csv_file} --dry-run")
        
        elif choice == '5':
            break
        
        else:
            print("Invalid choice. Please enter 1-5.")

def main():
    """Main quick start function"""
    show_welcome()
    
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please run setup.py first.")
        return 1
    
    show_csv_format()
    show_sample_csv()
    show_usage_examples()
    
    # Ask if user wants interactive demo
    demo = input("Would you like to run the interactive demo? (y/n): ").strip().lower()
    if demo == 'y':
        interactive_demo()
    
    print("\n🎉 Quick start completed!")
    print("\nFor more information, see README.md")
    print("For help: python main.py --help")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
