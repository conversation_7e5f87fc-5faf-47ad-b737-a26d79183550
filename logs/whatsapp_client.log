2025-06-12 16:50:00,493 - ERROR - WhatsApp API connection test failed: WhatsApp API returned status 404
2025-06-12 16:55:22,322 - ERROR - WhatsApp API connection test failed: WhatsApp API returned status 404
2025-06-12 16:57:45,626 - INFO - WhatsApp API connection test: WhatsApp API connection successful
2025-06-12 16:57:51,342 - INFO - WhatsApp API connection test: WhatsApp API connection successful
2025-06-12 16:57:51,342 - INFO - Starting to process CSV file: sample_data/sample_properties.csv
2025-06-12 16:57:51,344 - INFO - CSV headers validated: ['contact_name', 'contact_phone', 'property_url', 'property_title', 'property_price', 'property_address', 'bedrooms', 'area']
2025-06-12 16:57:51,344 - INFO - Successfully read 4 rows from sample_data/sample_properties.csv
2025-06-12 16:57:51,344 - INFO - Successfully loaded 4 records from CSV
2025-06-12 16:57:51,344 - <PERSON>FO - Prepared 4 messages for sending
2025-06-12 16:57:51,344 - INFO - Starting batch send of 4 messages
2025-06-12 16:57:51,344 - INFO - Sending message to 6591234567
2025-06-12 16:57:52,056 - INFO - ✅ Message 1/4 sent successfully to 6591234567
2025-06-12 16:57:55,059 - INFO - Sending message to 6598765432
2025-06-12 16:57:55,699 - INFO - ✅ Message 2/4 sent successfully to 6598765432
2025-06-12 16:57:58,700 - INFO - Sending message to 6587654321
2025-06-12 16:58:00,028 - INFO - ✅ Message 3/4 sent successfully to 6587654321
2025-06-12 16:58:03,030 - INFO - Sending message to 6576543210
2025-06-12 16:58:03,416 - INFO - ✅ Message 4/4 sent successfully to 6576543210
2025-06-12 16:58:03,417 - INFO - Batch completed: 4/4 successful (100.0%)
2025-06-12 16:58:03,418 - INFO - Results saved to output/sending_results_20250612_165803.csv
2025-06-12 16:58:03,418 - INFO - Success report saved to output/success_report_20250612_165803.txt
2025-06-12 16:58:03,418 - INFO - Processing completed successfully
